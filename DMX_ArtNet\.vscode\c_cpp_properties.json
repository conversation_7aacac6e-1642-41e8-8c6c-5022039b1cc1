{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**", "${workspaceFolder}/lwip/src/include", "${workspaceFolder}/Drivers/CMSIS/Device/ST/STM32G4xx/Include"], "defines": ["_DEBUG", "UNICODE", "_UNICODE"], "cStandard": "c17", "cppStandard": "c++17", "compilerPath": "C:\\Program Files\\ST\\STM32CubeIDE_1.13.1\\STM32CubeIDE\\plugins\\com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.11.3.rel1.win32_1.1.0.202305231506\\tools\\bin\\arm-none-eabi-gcc.exe", "intelliSenseMode": "gcc-arm"}], "version": 4}